# Sprint-2 详细任务计划 - 学科管理完整功能

## 文档信息

| 项目 | 内容 |
|------|------|
| **Sprint名称** | Sprint-2: 学科管理完整功能 |
| **任务ID** | `bd9cccc9-70be-4553-bf61-671fe0f3f2ca` |
| **优先级** | P0（最高优先级） |
| **预估工期** | 2-3天 |
| **负责人** | Alex (工程师) |
| **依赖任务** | Sprint-1: 项目基础设施搭建 |
| **文档版本** | v1.0.0 |
| **创建日期** | 2025-01-08 |
| **规划负责人** | Mike (团队领袖) & Bob (架构师) |
| **状态** | 待执行 |

## Sprint概述

基于《PRD_期末复习平台_v1.0.md》、《Overall_Architecture_期末复习平台.md》和《Task_Planning_期末复习平台_v1.1.md》，将"学科管理完整功能"切片分解为5个具体的、可执行的子任务。实现学科的创建、查看、删除功能，包括完整的前端界面和后端API，支持学科名称唯一性验证、长度限制、删除二次确认等业务规则。

## 核心业务规则

1. **学科名称唯一性**：系统中不允许存在重复的学科名称
2. **学科名称长度限制**：1-50个字符
3. **删除二次确认**：删除学科时必须进行二次确认
4. **级联删除**：删除学科会同时删除其下所有内容
5. **权限控制**：创建和删除学科需要管理员权限

## 核心设计原则

1. **API契约优先**：所有开发工作以API契约为"法律合同"
2. **测试驱动开发**：每个功能都必须有完整的测试覆盖
3. **用户体验优先**：提供清晰的操作反馈和错误提示
4. **数据一致性**：确保数据操作的原子性和一致性

---

## 任务2.0: API契约设计与评审 (API Contract Design & Review)

**优先级**: P-1 (最高)
**预估时间**: 4-6小时
**负责人**: Bob (架构师) + Alex (工程师)

### 任务描述
在所有开发工作开始前，首先要明确定义本次切片所需的所有API接口的详细规格。这份规格就是前后端协作的"法律合同"，是后续所有开发、测试工作的唯一依据。

### 具体实施步骤

#### 2.0.1 设计API端点、方法与数据结构 (DTOs)
明确所有端点、HTTP方法、请求体和响应体的确切字段名与数据类型。

**学科管理API接口设计**：

```javascript
// 1. 获取所有学科列表
GET /api/v1/subjects
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "高等数学",
      "description": "高等数学复习资料",
      "created_at": "2025-01-08T10:00:00.000Z",
      "updated_at": "2025-01-08T10:00:00.000Z",
      "file_count": 15,
      "total_size": 2048576
    }
  ],
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 2. 创建新学科
POST /api/v1/subjects
Request: {
  "name": "高等数学",
  "description": "高等数学复习资料"
}
Response: {
  "success": true,
  "code": 201,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "高等数学",
    "description": "高等数学复习资料",
    "created_at": "2025-01-08T10:00:00.000Z",
    "updated_at": "2025-01-08T10:00:00.000Z",
    "file_count": 0,
    "total_size": 0
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 3. 获取学科详情
GET /api/v1/subjects/:id
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "高等数学",
    "description": "高等数学复习资料",
    "created_at": "2025-01-08T10:00:00.000Z",
    "updated_at": "2025-01-08T10:00:00.000Z",
    "file_count": 15,
    "total_size": 2048576
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 4. 更新学科信息
PUT /api/v1/subjects/:id
Request: {
  "name": "高等数学A",
  "description": "高等数学A复习资料"
}
Response: {
  "success": true,
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "高等数学A",
    "description": "高等数学A复习资料",
    "created_at": "2025-01-08T10:00:00.000Z",
    "updated_at": "2025-01-08T10:05:00.000Z",
    "file_count": 15,
    "total_size": 2048576
  },
  "timestamp": "2025-01-08T10:05:00.000Z"
}

// 5. 删除学科
DELETE /api/v1/subjects/:id
Response: {
  "success": true,
  "code": 200,
  "message": "删除成功",
  "data": {
    "deleted_subject_id": 1,
    "deleted_files_count": 15,
    "freed_space": 2048576
  },
  "timestamp": "2025-01-08T10:10:00.000Z"
}

// 6. 检查学科名称唯一性
GET /api/v1/subjects/check-name?name=高等数学
Response: {
  "success": true,
  "code": 200,
  "message": "检查完成",
  "data": {
    "name": "高等数学",
    "is_available": false,
    "existing_subject_id": 1
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 2.0.2 确定状态码与统一错误格式
明确所有成功和失败场景下的HTTP状态码，并统一错误响应的JSON结构。

**错误响应格式**：
```javascript
// 学科名称重复错误
{
  "success": false,
  "code": 409,
  "message": "学科名称已存在",
  "error": "SUBJECT_NAME_DUPLICATE",
  "details": {
    "field": "name",
    "value": "高等数学",
    "existing_subject_id": 1
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 学科名称长度错误
{
  "success": false,
  "code": 400,
  "message": "学科名称长度必须在1-50个字符之间",
  "error": "SUBJECT_NAME_LENGTH_INVALID",
  "details": {
    "field": "name",
    "value": "",
    "min_length": 1,
    "max_length": 50,
    "current_length": 0
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 学科不存在错误
{
  "success": false,
  "code": 404,
  "message": "学科不存在",
  "error": "SUBJECT_NOT_FOUND",
  "details": {
    "subject_id": 999
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 权限不足错误
{
  "success": false,
  "code": 403,
  "message": "权限不足，需要管理员权限",
  "error": "INSUFFICIENT_PERMISSIONS",
  "details": {
    "required_role": "admin",
    "current_role": "guest"
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 2.0.3 编写初始文档
在 `/docs/architecture/API_Reference.md` 中更新API契约文档。

#### 2.0.4 评审与冻结
由项目负责人最终评审并"冻结"此API契约。

### 验收标准
- [ ] 一份详细的、已评审通过的学科管理API契约文档已创建
- [ ] 所有相关人员都已理解并同意遵守此契约
- [ ] API契约文档已更新到 `/docs/architecture/API_Reference.md`
- [ ] 响应格式和错误处理标准已明确定义
- [ ] 数据验证规则已明确定义

### 相关文件
- `/docs/architecture/API_Reference.md` - API契约文档 (更新)
- `/docs/architecture/Backend_Architecture_and_Guide.md` - 后端架构指南 (参考)

---

## 任务2.1: 环境与数据模型准备 (Setup)

**优先级**: P0 (最高)
**预估时间**: 3-4小时
**负责人**: Alex (工程师)
**依赖任务**: 任务2.0 (API契约设计与评审)

### 任务描述
基于任务2.0中已确定的API契约，确保数据库表结构完全支持学科管理功能，包括数据验证、索引优化和业务规则实现。

### 具体实施步骤

#### 2.1.1 验证数据库表结构
确保subjects表结构支持所有业务需求：

```sql
-- 验证现有subjects表结构
SELECT sql FROM sqlite_master WHERE type='table' AND name='subjects';

-- 如需要，添加缺失字段
ALTER TABLE subjects ADD COLUMN description TEXT;
ALTER TABLE subjects ADD COLUMN file_count INTEGER DEFAULT 0;
ALTER TABLE subjects ADD COLUMN total_size INTEGER DEFAULT 0;

-- 创建必要的索引
CREATE INDEX IF NOT EXISTS idx_subjects_name ON subjects(name);
CREATE INDEX IF NOT EXISTS idx_subjects_created_at ON subjects(created_at);
```

#### 2.1.2 实现数据模型类
```javascript
// backend/src/models/Subject.js
class Subject {
  constructor(db) {
    this.db = db;
  }

  // 创建学科
  create(data) {
    const stmt = this.db.prepare(`
      INSERT INTO subjects (name, description, created_at, updated_at)
      VALUES (?, ?, datetime('now'), datetime('now'))
    `);
    return stmt.run(data.name, data.description || null);
  }

  // 获取所有学科
  findAll() {
    const stmt = this.db.prepare(`
      SELECT id, name, description, created_at, updated_at,
             file_count, total_size
      FROM subjects
      ORDER BY created_at DESC
    `);
    return stmt.all();
  }

  // 根据ID获取学科
  findById(id) {
    const stmt = this.db.prepare(`
      SELECT id, name, description, created_at, updated_at,
             file_count, total_size
      FROM subjects
      WHERE id = ?
    `);
    return stmt.get(id);
  }

  // 根据名称查找学科
  findByName(name) {
    const stmt = this.db.prepare(`
      SELECT id, name, description, created_at, updated_at
      FROM subjects
      WHERE name = ?
    `);
    return stmt.get(name);
  }

  // 更新学科
  update(id, data) {
    const stmt = this.db.prepare(`
      UPDATE subjects
      SET name = ?, description = ?, updated_at = datetime('now')
      WHERE id = ?
    `);
    return stmt.run(data.name, data.description || null, id);
  }

  // 删除学科
  delete(id) {
    const stmt = this.db.prepare('DELETE FROM subjects WHERE id = ?');
    return stmt.run(id);
  }

  // 更新文件统计
  updateFileStats(id, fileCount, totalSize) {
    const stmt = this.db.prepare(`
      UPDATE subjects
      SET file_count = ?, total_size = ?, updated_at = datetime('now')
      WHERE id = ?
    `);
    return stmt.run(fileCount, totalSize, id);
  }
}

module.exports = Subject;
```

#### 2.1.3 创建数据验证工具
```javascript
// backend/src/utils/subjectValidator.js
class SubjectValidator {
  static validateName(name) {
    const errors = [];

    if (!name || typeof name !== 'string') {
      errors.push('学科名称不能为空');
    } else {
      const trimmedName = name.trim();
      if (trimmedName.length < 1) {
        errors.push('学科名称不能为空');
      } else if (trimmedName.length > 50) {
        errors.push('学科名称长度不能超过50个字符');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedName: name ? name.trim() : ''
    };
  }

  static validateDescription(description) {
    if (description && typeof description === 'string' && description.length > 500) {
      return {
        isValid: false,
        errors: ['学科描述长度不能超过500个字符'],
        sanitizedDescription: description.trim()
      };
    }

    return {
      isValid: true,
      errors: [],
      sanitizedDescription: description ? description.trim() : null
    };
  }

  static validateCreateData(data) {
    const nameValidation = this.validateName(data.name);
    const descValidation = this.validateDescription(data.description);

    return {
      isValid: nameValidation.isValid && descValidation.isValid,
      errors: [...nameValidation.errors, ...descValidation.errors],
      sanitizedData: {
        name: nameValidation.sanitizedName,
        description: descValidation.sanitizedDescription
      }
    };
  }
}

module.exports = SubjectValidator;
```

### 验收标准
- [ ] 数据库文件和表结构创建成功，其字段和类型与API契约中的数据结构完全对应
- [ ] Subject数据模型类实现完成，支持所有CRUD操作
- [ ] 数据验证工具实现完成，支持业务规则验证
- [ ] 数据库索引创建完成，查询性能优化
- [ ] 更新后端指南文档 (`/docs/architecture/Backend_Architecture_and_Guide.md`)

### 相关文件
- `backend/src/models/Subject.js` - 学科数据模型 (创建)
- `backend/src/utils/subjectValidator.js` - 数据验证工具 (创建)
- `backend/src/config/database.js` - 数据库配置 (更新)
- `/docs/architecture/Backend_Architecture_and_Guide.md` - 后端架构指南 (更新)

---

## 任务2.2: 后端API开发与测试闭环 (Backend Loop)

**优先级**: P0 (最高)
**预估时间**: 8-10小时
**负责人**: Alex (工程师)
**依赖任务**: 任务2.1 (环境与数据模型准备)

### 任务描述
严格按照任务2.0的API契约，实现所有学科管理API接口。采用TDD开发模式，先写测试，再写实现，确保代码质量和业务逻辑正确性。

### 具体实施步骤

#### 2.2.1 创建学科服务层 (TDD方式)

**第一步：编写服务层测试**
```javascript
// tests/unit/subjectService.test.js
const SubjectService = require('../../src/services/subjectService');
const Subject = require('../../src/models/Subject');

describe('SubjectService', () => {
  let subjectService;
  let mockSubjectModel;

  beforeEach(() => {
    mockSubjectModel = {
      findAll: jest.fn(),
      findById: jest.fn(),
      findByName: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    };
    subjectService = new SubjectService(mockSubjectModel);
  });

  describe('getAllSubjects', () => {
    test('should return all subjects', async () => {
      const mockSubjects = [
        { id: 1, name: '高等数学', description: '高等数学复习资料' }
      ];
      mockSubjectModel.findAll.mockReturnValue(mockSubjects);

      const result = await subjectService.getAllSubjects();

      expect(result).toEqual(mockSubjects);
      expect(mockSubjectModel.findAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('createSubject', () => {
    test('should create subject successfully', async () => {
      const subjectData = { name: '高等数学', description: '高等数学复习资料' };
      const mockResult = { lastInsertRowid: 1 };
      const mockCreatedSubject = { id: 1, ...subjectData };

      mockSubjectModel.findByName.mockReturnValue(null);
      mockSubjectModel.create.mockReturnValue(mockResult);
      mockSubjectModel.findById.mockReturnValue(mockCreatedSubject);

      const result = await subjectService.createSubject(subjectData);

      expect(result).toEqual(mockCreatedSubject);
      expect(mockSubjectModel.findByName).toHaveBeenCalledWith('高等数学');
      expect(mockSubjectModel.create).toHaveBeenCalledWith(subjectData);
    });

    test('should throw error if subject name already exists', async () => {
      const subjectData = { name: '高等数学', description: '高等数学复习资料' };
      const existingSubject = { id: 1, name: '高等数学' };

      mockSubjectModel.findByName.mockReturnValue(existingSubject);

      await expect(subjectService.createSubject(subjectData))
        .rejects.toThrow('学科名称已存在');
    });
  });
});
```

**第二步：实现服务层**
```javascript
// backend/src/services/subjectService.js
const SubjectValidator = require('../utils/subjectValidator');

class SubjectService {
  constructor(subjectModel) {
    this.Subject = subjectModel;
  }

  async getAllSubjects() {
    return this.Subject.findAll();
  }

  async getSubjectById(id) {
    const subject = this.Subject.findById(id);
    if (!subject) {
      const error = new Error('学科不存在');
      error.code = 'SUBJECT_NOT_FOUND';
      error.status = 404;
      throw error;
    }
    return subject;
  }

  async createSubject(data) {
    // 数据验证
    const validation = SubjectValidator.validateCreateData(data);
    if (!validation.isValid) {
      const error = new Error(validation.errors.join(', '));
      error.code = 'VALIDATION_ERROR';
      error.status = 400;
      error.details = validation.errors;
      throw error;
    }

    // 检查名称唯一性
    const existingSubject = this.Subject.findByName(validation.sanitizedData.name);
    if (existingSubject) {
      const error = new Error('学科名称已存在');
      error.code = 'SUBJECT_NAME_DUPLICATE';
      error.status = 409;
      error.details = {
        field: 'name',
        value: validation.sanitizedData.name,
        existing_subject_id: existingSubject.id
      };
      throw error;
    }

    // 创建学科
    const result = this.Subject.create(validation.sanitizedData);
    return this.Subject.findById(result.lastInsertRowid);
  }

  async updateSubject(id, data) {
    // 检查学科是否存在
    const existingSubject = this.Subject.findById(id);
    if (!existingSubject) {
      const error = new Error('学科不存在');
      error.code = 'SUBJECT_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    // 数据验证
    const validation = SubjectValidator.validateCreateData(data);
    if (!validation.isValid) {
      const error = new Error(validation.errors.join(', '));
      error.code = 'VALIDATION_ERROR';
      error.status = 400;
      throw error;
    }

    // 检查名称唯一性（排除自己）
    const duplicateSubject = this.Subject.findByName(validation.sanitizedData.name);
    if (duplicateSubject && duplicateSubject.id !== id) {
      const error = new Error('学科名称已存在');
      error.code = 'SUBJECT_NAME_DUPLICATE';
      error.status = 409;
      throw error;
    }

    // 更新学科
    this.Subject.update(id, validation.sanitizedData);
    return this.Subject.findById(id);
  }

  async deleteSubject(id) {
    // 检查学科是否存在
    const existingSubject = this.Subject.findById(id);
    if (!existingSubject) {
      const error = new Error('学科不存在');
      error.code = 'SUBJECT_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    // 删除学科
    const result = this.Subject.delete(id);

    return {
      deleted_subject_id: id,
      deleted_files_count: existingSubject.file_count || 0,
      freed_space: existingSubject.total_size || 0
    };
  }

  async checkNameAvailability(name) {
    const validation = SubjectValidator.validateName(name);
    if (!validation.isValid) {
      const error = new Error(validation.errors.join(', '));
      error.code = 'VALIDATION_ERROR';
      error.status = 400;
      throw error;
    }

    const existingSubject = this.Subject.findByName(validation.sanitizedName);

    return {
      name: validation.sanitizedName,
      is_available: !existingSubject,
      existing_subject_id: existingSubject ? existingSubject.id : null
    };
  }
}

module.exports = SubjectService;
```

#### 2.2.2 创建控制器层 (TDD方式)

**第一步：编写控制器测试**
```javascript
// tests/integration/subjectController.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('Subject Controller', () => {
  beforeEach(async () => {
    // 清理测试数据
    await clearTestData();
  });

  describe('GET /api/v1/subjects', () => {
    test('should return empty array when no subjects exist', async () => {
      const response = await request(app.callback())
        .get('/api/v1/subjects')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功',
        data: []
      });
    });

    test('should return subjects list', async () => {
      // 先创建测试数据
      await createTestSubject({ name: '高等数学', description: '高等数学复习资料' });

      const response = await request(app.callback())
        .get('/api/v1/subjects')
        .expect(200);

      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toMatchObject({
        name: '高等数学',
        description: '高等数学复习资料'
      });
    });
  });

  describe('POST /api/v1/subjects', () => {
    test('should create subject successfully', async () => {
      const subjectData = {
        name: '高等数学',
        description: '高等数学复习资料'
      };

      const response = await request(app.callback())
        .post('/api/v1/subjects')
        .send(subjectData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        code: 201,
        message: '创建成功'
      });
      expect(response.body.data).toMatchObject(subjectData);
      expect(response.body.data.id).toBeDefined();
    });

    test('should return error for duplicate subject name', async () => {
      const subjectData = { name: '高等数学', description: '高等数学复习资料' };

      // 先创建一个学科
      await createTestSubject(subjectData);

      // 尝试创建重复名称的学科
      const response = await request(app.callback())
        .post('/api/v1/subjects')
        .send(subjectData)
        .expect(409);

      expect(response.body).toMatchObject({
        success: false,
        code: 409,
        message: '学科名称已存在',
        error: 'SUBJECT_NAME_DUPLICATE'
      });
    });

    test('should return error for invalid subject name', async () => {
      const response = await request(app.callback())
        .post('/api/v1/subjects')
        .send({ name: '', description: '描述' })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        code: 400,
        message: '学科名称不能为空'
      });
    });
  });

  describe('DELETE /api/v1/subjects/:id', () => {
    test('should delete subject successfully', async () => {
      const subject = await createTestSubject({ name: '高等数学' });

      const response = await request(app.callback())
        .delete(`/api/v1/subjects/${subject.id}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '删除成功'
      });
      expect(response.body.data.deleted_subject_id).toBe(subject.id);
    });

    test('should return error for non-existent subject', async () => {
      const response = await request(app.callback())
        .delete('/api/v1/subjects/999')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        code: 404,
        message: '学科不存在'
      });
    });
  });
});
```

**第二步：实现控制器**
```javascript
// backend/src/controllers/subjectController.js
const SubjectService = require('../services/subjectService');
const Subject = require('../models/Subject');

class SubjectController {
  constructor() {
    this.subjectService = new SubjectService(new Subject(global.db));
  }

  async getAllSubjects(ctx) {
    try {
      const subjects = await this.subjectService.getAllSubjects();

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: subjects,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getSubjectById(ctx) {
    try {
      const { id } = ctx.params;
      const subject = await this.subjectService.getSubjectById(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: subject,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async createSubject(ctx) {
    try {
      const subjectData = ctx.request.body;
      const subject = await this.subjectService.createSubject(subjectData);

      ctx.status = 201;
      ctx.body = {
        success: true,
        code: 201,
        message: '创建成功',
        data: subject,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async updateSubject(ctx) {
    try {
      const { id } = ctx.params;
      const subjectData = ctx.request.body;
      const subject = await this.subjectService.updateSubject(parseInt(id), subjectData);

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '更新成功',
        data: subject,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteSubject(ctx) {
    try {
      const { id } = ctx.params;
      const result = await this.subjectService.deleteSubject(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '删除成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async checkNameAvailability(ctx) {
    try {
      const { name } = ctx.query;
      const result = await this.subjectService.checkNameAvailability(name);

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '检查完成',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = SubjectController;
```

#### 2.2.3 配置路由
```javascript
// backend/src/routes/subjects.js
const Router = require('koa-router');
const SubjectController = require('../controllers/subjectController');

const router = new Router({ prefix: '/api/v1/subjects' });
const subjectController = new SubjectController();

// 获取所有学科
router.get('/', subjectController.getAllSubjects.bind(subjectController));

// 检查学科名称可用性
router.get('/check-name', subjectController.checkNameAvailability.bind(subjectController));

// 获取学科详情
router.get('/:id', subjectController.getSubjectById.bind(subjectController));

// 创建学科
router.post('/', subjectController.createSubject.bind(subjectController));

// 更新学科
router.put('/:id', subjectController.updateSubject.bind(subjectController));

// 删除学科
router.delete('/:id', subjectController.deleteSubject.bind(subjectController));

module.exports = router;
```

#### 2.2.4 运行测试并修复Bug
```bash
# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行所有测试
npm test

# 如果测试失败，立即修复API的Bug，重新运行测试
# 确保其行为与API契约100%一致
```

### 验收标准
- [ ] 所有学科管理API接口按照契约实现完成
- [ ] 编写测试脚本进行测试，测试覆盖率 > 90%
- [ ] 如果测试失败，立即修复API的Bug，重新运行测试
- [ ] 确保API行为与契约100%一致
- [ ] 任何API的变更都需要更新API参考文档 (`/docs/architecture/API_Reference.md`)
- [ ] 业务规则验证正确实现（名称唯一性、长度限制等）
- [ ] 错误处理和状态码符合契约规范

### 相关文件
- `backend/src/services/subjectService.js` - 学科业务逻辑服务 (创建)
- `backend/src/controllers/subjectController.js` - 学科管理控制器 (创建)
- `backend/src/routes/subjects.js` - 学科路由配置 (创建)
- `tests/unit/subjectService.test.js` - 服务层单元测试 (创建)
- `tests/integration/subjectController.test.js` - 控制器集成测试 (创建)
- `/docs/architecture/API_Reference.md` - API参考文档 (更新)

---

## 任务2.3: 前端UI开发与测试闭环 (Frontend Loop)

**优先级**: P0 (最高)
**预估时间**: 8-10小时
**负责人**: Alex (工程师)
**依赖任务**: 任务2.2 (后端API开发与测试闭环)

### 任务描述
严格基于任务2.0的API契约，进行前端开发。实现学科管理的完整用户界面，包括学科列表、创建表单、编辑功能、删除确认等，确保用户体验优秀。

### 具体实施步骤

#### 2.3.1 更新API服务层
```typescript
// frontend/src/services/subjectApi.ts
import api from './api'

export interface Subject {
  id?: number
  name: string
  description?: string
  created_at?: string
  updated_at?: string
  file_count?: number
  total_size?: number
}

export interface CreateSubjectRequest {
  name: string
  description?: string
}

export interface UpdateSubjectRequest {
  name: string
  description?: string
}

export interface SubjectResponse {
  success: boolean
  code: number
  message: string
  data: Subject
  timestamp: string
}

export interface SubjectsListResponse {
  success: boolean
  code: number
  message: string
  data: Subject[]
  timestamp: string
}

export interface DeleteSubjectResponse {
  success: boolean
  code: number
  message: string
  data: {
    deleted_subject_id: number
    deleted_files_count: number
    freed_space: number
  }
  timestamp: string
}

export interface NameCheckResponse {
  success: boolean
  code: number
  message: string
  data: {
    name: string
    is_available: boolean
    existing_subject_id?: number
  }
  timestamp: string
}

export const subjectApi = {
  // 获取所有学科
  getAll: (): Promise<SubjectsListResponse> =>
    api.get('/subjects'),

  // 获取学科详情
  getById: (id: number): Promise<SubjectResponse> =>
    api.get(`/subjects/${id}`),

  // 创建学科
  create: (data: CreateSubjectRequest): Promise<SubjectResponse> =>
    api.post('/subjects', data),

  // 更新学科
  update: (id: number, data: UpdateSubjectRequest): Promise<SubjectResponse> =>
    api.put(`/subjects/${id}`, data),

  // 删除学科
  delete: (id: number): Promise<DeleteSubjectResponse> =>
    api.delete(`/subjects/${id}`),

  // 检查名称可用性
  checkName: (name: string): Promise<NameCheckResponse> =>
    api.get(`/subjects/check-name?name=${encodeURIComponent(name)}`)
}
```

#### 2.3.2 实现学科卡片组件
```vue
<!-- frontend/src/components/SubjectCard.vue -->
<template>
  <a-card
    class="subject-card"
    :hoverable="true"
    :loading="loading"
  >
    <template #actions>
      <EditOutlined @click="handleEdit" title="编辑学科" />
      <DeleteOutlined @click="handleDelete" title="删除学科" class="delete-action" />
    </template>

    <a-card-meta>
      <template #title>
        <div class="subject-title">
          {{ subject.name }}
        </div>
      </template>

      <template #description>
        <div class="subject-description">
          {{ subject.description || '暂无描述' }}
        </div>

        <div class="subject-stats">
          <a-space>
            <span>
              <FileOutlined />
              {{ subject.file_count || 0 }} 个文件
            </span>
            <span>
              <DatabaseOutlined />
              {{ formatFileSize(subject.total_size || 0) }}
            </span>
          </a-space>
        </div>

        <div class="subject-time">
          创建时间: {{ formatDate(subject.created_at) }}
        </div>
      </template>
    </a-card-meta>
  </a-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined, FileOutlined, DatabaseOutlined } from '@ant-design/icons-vue'
import type { Subject } from '@/services/subjectApi'
import { subjectApi } from '@/services/subjectApi'

interface Props {
  subject: Subject
}

interface Emits {
  (e: 'edit', subject: Subject): void
  (e: 'delete', subjectId: number): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)

const handleEdit = () => {
  emit('edit', props.subject)
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除学科"${props.subject.name}"吗？此操作将同时删除该学科下的所有文件，且不可恢复。`,
    okText: '确定删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        loading.value = true
        await subjectApi.delete(props.subject.id!)
        message.success('删除成功')
        emit('delete', props.subject.id!)
        emit('refresh')
      } catch (error: any) {
        message.error(error.response?.data?.message || '删除失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString?: string): string => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.subject-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.subject-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.subject-title {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.subject-description {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.5;
}

.subject-stats {
  margin-bottom: 8px;
  color: #999;
  font-size: 12px;
}

.subject-time {
  color: #ccc;
  font-size: 11px;
}

.delete-action {
  color: #ff4d4f;
}

.delete-action:hover {
  color: #ff7875;
}
</style>
```

#### 2.3.3 实现创建/编辑学科弹窗
```vue
<!-- frontend/src/components/SubjectModal.vue -->
<template>
  <a-modal
    :visible="visible"
    :title="isEdit ? '编辑学科' : '创建学科'"
    :ok-text="isEdit ? '更新' : '创建'"
    cancel-text="取消"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :width="600"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <a-form-item
        label="学科名称"
        name="name"
        :validate-status="nameValidateStatus"
        :help="nameValidateMessage"
      >
        <a-input
          v-model:value="formData.name"
          placeholder="请输入学科名称（1-50个字符）"
          :maxlength="50"
          show-count
          @blur="checkNameAvailability"
          @input="handleNameInput"
        />
      </a-form-item>

      <a-form-item
        label="学科描述"
        name="description"
      >
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入学科描述（可选，最多500个字符）"
          :maxlength="500"
          :rows="4"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { subjectApi, type Subject, type CreateSubjectRequest, type UpdateSubjectRequest } from '@/services/subjectApi'

interface Props {
  visible: boolean
  subject?: Subject | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const nameValidateStatus = ref<'success' | 'warning' | 'error' | 'validating' | ''>('')
const nameValidateMessage = ref('')

const isEdit = computed(() => !!props.subject?.id)

const formData = reactive<CreateSubjectRequest>({
  name: '',
  description: ''
})

const formRules = {
  name: [
    { required: true, message: '请输入学科名称' },
    { min: 1, max: 50, message: '学科名称长度必须在1-50个字符之间' }
  ],
  description: [
    { max: 500, message: '学科描述长度不能超过500个字符' }
  ]
}

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (visible) => {
  if (visible) {
    if (props.subject) {
      // 编辑模式，填充现有数据
      formData.name = props.subject.name
      formData.description = props.subject.description || ''
    } else {
      // 创建模式，重置表单
      formData.name = ''
      formData.description = ''
    }

    // 重置验证状态
    nameValidateStatus.value = ''
    nameValidateMessage.value = ''

    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }
})

const handleNameInput = () => {
  // 输入时清除验证状态
  nameValidateStatus.value = ''
  nameValidateMessage.value = ''
}

const checkNameAvailability = async () => {
  if (!formData.name.trim()) return

  // 编辑模式下，如果名称没有变化，不需要检查
  if (isEdit.value && formData.name === props.subject?.name) {
    nameValidateStatus.value = 'success'
    nameValidateMessage.value = ''
    return
  }

  try {
    nameValidateStatus.value = 'validating'
    nameValidateMessage.value = '检查名称可用性...'

    const response = await subjectApi.checkName(formData.name.trim())

    if (response.data.is_available) {
      nameValidateStatus.value = 'success'
      nameValidateMessage.value = '名称可用'
    } else {
      nameValidateStatus.value = 'error'
      nameValidateMessage.value = '学科名称已存在'
    }
  } catch (error: any) {
    nameValidateStatus.value = 'error'
    nameValidateMessage.value = error.response?.data?.message || '检查失败'
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    // 如果名称验证失败，不允许提交
    if (nameValidateStatus.value === 'error') {
      message.error('请解决表单验证错误后再提交')
      return
    }

    loading.value = true

    const submitData = {
      name: formData.name.trim(),
      description: formData.description?.trim() || undefined
    }

    if (isEdit.value) {
      await subjectApi.update(props.subject!.id!, submitData as UpdateSubjectRequest)
      message.success('更新成功')
    } else {
      await subjectApi.create(submitData)
      message.success('创建成功')
    }

    emit('success')
    handleCancel()
  } catch (error: any) {
    message.error(error.response?.data?.message || (isEdit.value ? '更新失败' : '创建失败'))
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}
</script>
```

#### 2.3.4 实现学科列表页面
```vue
<!-- frontend/src/pages/SubjectManagement.vue -->
<template>
  <div class="subject-management">
    <a-layout>
      <a-layout-header class="header">
        <div class="header-content">
          <h1>学科管理</h1>
          <a-button type="primary" @click="showCreateModal" :icon="h(PlusOutlined)">
            添加学科
          </a-button>
        </div>
      </a-layout-header>

      <a-layout-content class="content">
        <div class="subjects-container">
          <a-spin :spinning="loading" tip="加载中...">
            <div v-if="subjects.length === 0 && !loading" class="empty-state">
              <a-empty description="暂无学科">
                <a-button type="primary" @click="showCreateModal">
                  创建第一个学科
                </a-button>
              </a-empty>
            </div>

            <div v-else class="subjects-grid">
              <SubjectCard
                v-for="subject in subjects"
                :key="subject.id"
                :subject="subject"
                @edit="handleEdit"
                @delete="handleDelete"
                @refresh="loadSubjects"
              />
            </div>
          </a-spin>
        </div>
      </a-layout-content>
    </a-layout>

    <SubjectModal
      v-model:visible="modalVisible"
      :subject="editingSubject"
      @success="handleModalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { subjectApi, type Subject } from '@/services/subjectApi'
import SubjectCard from '@/components/SubjectCard.vue'
import SubjectModal from '@/components/SubjectModal.vue'

const subjects = ref<Subject[]>([])
const loading = ref(false)
const modalVisible = ref(false)
const editingSubject = ref<Subject | null>(null)

const loadSubjects = async () => {
  try {
    loading.value = true
    const response = await subjectApi.getAll()
    subjects.value = response.data
  } catch (error: any) {
    message.error(error.response?.data?.message || '加载学科列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateModal = () => {
  editingSubject.value = null
  modalVisible.value = true
}

const handleEdit = (subject: Subject) => {
  editingSubject.value = subject
  modalVisible.value = true
}

const handleDelete = (subjectId: number) => {
  // 从列表中移除已删除的学科
  subjects.value = subjects.value.filter(s => s.id !== subjectId)
}

const handleModalSuccess = () => {
  loadSubjects()
}

onMounted(() => {
  loadSubjects()
})
</script>

<style scoped>
.subject-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-content h1 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.content {
  padding: 24px;
}

.subjects-container {
  max-width: 1200px;
  margin: 0 auto;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

@media (max-width: 768px) {
  .subjects-grid {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    padding: 16px 0;
  }

  .content {
    padding: 16px;
  }
}
</style>
```

### 验收标准
- [ ] 前端项目能够正常启动和运行
- [ ] 学科管理界面实现完成（列表、创建、编辑、删除）
- [ ] API服务层实现完成，与后端API契约完全对应
- [ ] 组件测试覆盖率 > 80%
- [ ] 前端与后端API联调成功
- [ ] 用户体验优秀，操作反馈清晰
- [ ] 响应式设计，适配多种设备
- [ ] 错误处理和用户反馈机制完善
- [ ] 前端开发指南文档已更新 (`/docs/development/Frontend_Development_Guide.md`)

### 相关文件
- `frontend/src/services/subjectApi.ts` - 学科API服务 (更新)
- `frontend/src/components/SubjectCard.vue` - 学科卡片组件 (创建)
- `frontend/src/components/SubjectModal.vue` - 学科创建/编辑弹窗 (创建)
- `frontend/src/pages/SubjectManagement.vue` - 学科管理页面 (创建)
- `frontend/src/router/index.ts` - 路由配置 (更新)
- `/docs/development/Frontend_Development_Guide.md` - 前端开发指南 (更新)

---

## 任务2.4: 系统集成与端到端测试闭环 (E2E Loop)

**优先级**: P0 (最高)
**预估时间**: 6-8小时
**负责人**: Alex (工程师)
**依赖任务**: 任务2.3 (前端UI开发与测试闭环)

### 任务描述
进行前后端数据联调，验证连接通畅性。使用Playwright MCP进行端到端测试，模拟真实用户的学科管理操作流程。

### 具体实施步骤

#### 2.4.1 集成联调
进行前后端数据联调，验证连接通畅性。

**更新启动脚本**：
```json
// package.json (根目录)
{
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:backend": "cd backend && npm run dev",
    "dev:frontend": "cd frontend && npm run dev",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:integration": "./scripts/test-integration.sh"
  }
}
```

**集成测试环境配置**：
```javascript
// tests/e2e/setup.js
const { chromium } = require('@playwright/test');
const { spawn } = require('child_process');

let backendProcess;
let frontendProcess;

async function globalSetup() {
  // 启动后端服务
  backendProcess = spawn('npm', ['run', 'dev'], {
    cwd: './backend',
    stdio: 'pipe'
  });

  // 启动前端服务
  frontendProcess = spawn('npm', ['run', 'dev'], {
    cwd: './frontend',
    stdio: 'pipe'
  });

  // 等待服务启动
  await new Promise(resolve => setTimeout(resolve, 10000));

  // 验证服务可用性
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    await page.goto('http://localhost:5173');
    await page.waitForSelector('body', { timeout: 5000 });
  } catch (error) {
    throw new Error('Frontend service not available');
  }

  await browser.close();
}

async function globalTeardown() {
  if (backendProcess) backendProcess.kill();
  if (frontendProcess) frontendProcess.kill();
}

module.exports = { globalSetup, globalTeardown };
```

#### 2.4.2 端到端测试 (使用Playwright MCP)
使用Playwright MCP，模拟真实用户的操作流程。

**学科管理E2E测试**：
```javascript
// tests/e2e/subject-management.spec.js
const { test, expect } = require('@playwright/test');

test.describe('学科管理功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问学科管理页面
    await page.goto('http://localhost:5173/subjects');

    // 等待页面加载完成
    await page.waitForSelector('.subject-management', { timeout: 10000 });
  });

  test('用户可以查看学科管理页面', async ({ page }) => {
    // 验证页面标题
    await expect(page).toHaveTitle(/期末复习平台/);

    // 验证页面标题
    await expect(page.locator('h1')).toContainText('学科管理');

    // 验证添加学科按钮存在
    await expect(page.locator('text=添加学科')).toBeVisible();
  });

  test('用户可以创建新学科', async ({ page }) => {
    // 点击添加学科按钮
    await page.click('text=添加学科');

    // 验证弹窗出现
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('.ant-modal-title')).toContainText('创建学科');

    // 填写学科信息
    await page.fill('input[placeholder*="请输入学科名称"]', '高等数学');
    await page.fill('textarea[placeholder*="请输入学科描述"]', '高等数学复习资料');

    // 等待名称可用性检查
    await page.waitForTimeout(1000);

    // 提交表单
    await page.click('text=创建');

    // 验证成功消息
    await expect(page.locator('.ant-message')).toContainText('创建成功');

    // 验证学科出现在列表中
    await expect(page.locator('text=高等数学')).toBeVisible();
    await expect(page.locator('text=高等数学复习资料')).toBeVisible();
  });

  test('用户不能创建重复名称的学科', async ({ page }) => {
    // 先创建一个学科
    await page.click('text=添加学科');
    await page.fill('input[placeholder*="请输入学科名称"]', '线性代数');
    await page.click('text=创建');
    await expect(page.locator('.ant-message')).toContainText('创建成功');

    // 尝试创建同名学科
    await page.click('text=添加学科');
    await page.fill('input[placeholder*="请输入学科名称"]', '线性代数');

    // 等待名称检查
    await page.waitForTimeout(1000);

    // 验证错误提示
    await expect(page.locator('text=学科名称已存在')).toBeVisible();

    // 验证创建按钮不可用或点击后显示错误
    await page.click('text=创建');
    await expect(page.locator('text=请解决表单验证错误后再提交')).toBeVisible();
  });

  test('用户可以编辑学科信息', async ({ page }) => {
    // 先创建一个学科
    await page.click('text=添加学科');
    await page.fill('input[placeholder*="请输入学科名称"]', '概率论');
    await page.fill('textarea[placeholder*="请输入学科描述"]', '概率论与数理统计');
    await page.click('text=创建');
    await expect(page.locator('.ant-message')).toContainText('创建成功');

    // 点击编辑按钮
    await page.click('.anticon-edit');

    // 验证编辑弹窗
    await expect(page.locator('.ant-modal-title')).toContainText('编辑学科');

    // 修改学科信息
    await page.fill('input[placeholder*="请输入学科名称"]', '概率论与数理统计');
    await page.fill('textarea[placeholder*="请输入学科描述"]', '概率论与数理统计复习资料');

    // 提交修改
    await page.click('text=更新');

    // 验证成功消息
    await expect(page.locator('.ant-message')).toContainText('更新成功');

    // 验证修改后的信息
    await expect(page.locator('text=概率论与数理统计')).toBeVisible();
    await expect(page.locator('text=概率论与数理统计复习资料')).toBeVisible();
  });

  test('用户可以删除学科', async ({ page }) => {
    // 先创建一个学科
    await page.click('text=添加学科');
    await page.fill('input[placeholder*="请输入学科名称"]', '待删除学科');
    await page.click('text=创建');
    await expect(page.locator('.ant-message')).toContainText('创建成功');

    // 点击删除按钮
    await page.click('.anticon-delete');

    // 验证确认对话框
    await expect(page.locator('.ant-modal-confirm')).toBeVisible();
    await expect(page.locator('.ant-modal-confirm-title')).toContainText('确认删除');
    await expect(page.locator('.ant-modal-confirm-content')).toContainText('待删除学科');

    // 确认删除
    await page.click('text=确定删除');

    // 验证成功消息
    await expect(page.locator('.ant-message')).toContainText('删除成功');

    // 验证学科已从列表中移除
    await expect(page.locator('text=待删除学科')).not.toBeVisible();
  });

  test('用户可以取消删除操作', async ({ page }) => {
    // 先创建一个学科
    await page.click('text=添加学科');
    await page.fill('input[placeholder*="请输入学科名称"]', '不删除学科');
    await page.click('text=创建');
    await expect(page.locator('.ant-message')).toContainText('创建成功');

    // 点击删除按钮
    await page.click('.anticon-delete');

    // 取消删除
    await page.click('text=取消');

    // 验证学科仍然存在
    await expect(page.locator('text=不删除学科')).toBeVisible();
  });

  test('表单验证正常工作', async ({ page }) => {
    // 点击添加学科按钮
    await page.click('text=添加学科');

    // 尝试提交空表单
    await page.click('text=创建');

    // 验证验证错误
    await expect(page.locator('text=请输入学科名称')).toBeVisible();

    // 输入过长的名称
    const longName = 'a'.repeat(51);
    await page.fill('input[placeholder*="请输入学科名称"]', longName);
    await page.click('text=创建');

    // 验证长度限制错误
    await expect(page.locator('text=学科名称长度必须在1-50个字符之间')).toBeVisible();
  });

  test('空状态显示正确', async ({ page }) => {
    // 如果没有学科，应该显示空状态
    const subjectCards = await page.locator('.subject-card').count();

    if (subjectCards === 0) {
      await expect(page.locator('.ant-empty')).toBeVisible();
      await expect(page.locator('text=暂无学科')).toBeVisible();
      await expect(page.locator('text=创建第一个学科')).toBeVisible();
    }
  });

  test('响应式设计在移动端正常工作', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });

    // 验证页面在移动端正常显示
    await expect(page.locator('.subject-management')).toBeVisible();
    await expect(page.locator('text=添加学科')).toBeVisible();

    // 测试创建学科功能在移动端正常工作
    await page.click('text=添加学科');
    await expect(page.locator('.ant-modal')).toBeVisible();

    await page.fill('input[placeholder*="请输入学科名称"]', '移动端测试');
    await page.click('text=创建');

    await expect(page.locator('.ant-message')).toContainText('创建成功');
    await expect(page.locator('text=移动端测试')).toBeVisible();
  });
});

test.describe('API集成测试', () => {
  test('后端API健康检查', async ({ request }) => {
    const response = await request.get('http://localhost:3000/api/v1/health');
    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.status).toBe('healthy');
  });

  test('学科API端点正常工作', async ({ request }) => {
    // 测试获取学科列表
    const listResponse = await request.get('http://localhost:3000/api/v1/subjects');
    expect(listResponse.ok()).toBeTruthy();

    const listData = await listResponse.json();
    expect(listData.success).toBe(true);
    expect(Array.isArray(listData.data)).toBe(true);

    // 测试创建学科
    const createResponse = await request.post('http://localhost:3000/api/v1/subjects', {
      data: {
        name: 'API测试学科',
        description: 'API测试描述'
      }
    });
    expect(createResponse.ok()).toBeTruthy();

    const createData = await createResponse.json();
    expect(createData.success).toBe(true);
    expect(createData.data.name).toBe('API测试学科');

    // 测试删除学科
    const deleteResponse = await request.delete(`http://localhost:3000/api/v1/subjects/${createData.data.id}`);
    expect(deleteResponse.ok()).toBeTruthy();

    const deleteData = await deleteResponse.json();
    expect(deleteData.success).toBe(true);
  });
});
```

#### 2.4.3 修复与验证
修复在完整流程中发现的任何Bug。

**性能测试脚本**：
```javascript
// tests/e2e/performance.spec.js
const { test, expect } = require('@playwright/test');

test.describe('性能测试', () => {
  test('页面加载性能', async ({ page }) => {
    const startTime = Date.now();

    await page.goto('http://localhost:5173/subjects');
    await page.waitForSelector('.subject-management');

    const loadTime = Date.now() - startTime;

    // 页面加载时间应小于5秒
    expect(loadTime).toBeLessThan(5000);
  });

  test('API响应性能', async ({ request }) => {
    const startTime = Date.now();

    const response = await request.get('http://localhost:3000/api/v1/subjects');

    const responseTime = Date.now() - startTime;

    // API响应时间应小于3秒
    expect(responseTime).toBeLessThan(3000);
    expect(response.ok()).toBeTruthy();
  });
});
```

### 验收标准
- [ ] 前后端联调成功，数据传输正常
- [ ] 用户可以顺畅地完成整个学科管理流程
- [ ] 端到端测试100%通过
- [ ] 所有API接口响应时间 < 3秒
- [ ] 页面加载时间 < 5秒
- [ ] 错误处理机制完善，用户体验良好
- [ ] 响应式设计在各种设备上正常工作
- [ ] 更新用户交互清单文档 (`docs/prd/sprint-plans/用户交互清单_Sprint2.md`)
- [ ] 更新变更日志 (`/docs/CHANGELOG.md`)

### 相关文件
- `tests/e2e/subject-management.spec.js` - 学科管理E2E测试 (创建)
- `tests/e2e/performance.spec.js` - 性能测试 (创建)
- `tests/e2e/setup.js` - E2E测试环境配置 (创建)
- `playwright.config.js` - Playwright配置 (更新)
- `package.json` - 项目脚本配置 (更新)
- `docs/prd/sprint-plans/用户交互清单_Sprint2.md` - 用户交互清单 (创建)
- `/docs/CHANGELOG.md` - 变更日志 (更新)

---

## 质量保证与测试策略

### 测试覆盖率要求
- **单元测试覆盖率**: > 90%
- **集成测试覆盖率**: > 85%
- **E2E测试覆盖率**: 100% (核心用户流程)

### 代码质量标准
- **ESLint**: 无错误，警告 < 3个
- **TypeScript**: 严格模式，无any类型
- **代码审查**: 所有代码必须经过审查

### 性能要求
- **API响应时间**: < 3秒
- **页面加载时间**: < 5秒
- **数据库查询时间**: < 1秒

### 用户体验标准
- **操作反馈**: 所有操作都有明确的成功/失败反馈
- **错误处理**: 友好的错误提示，不暴露技术细节
- **响应式设计**: 适配桌面、平板、手机三种设备

---

## 风险管控

| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| 学科名称唯一性检查性能问题 | 中 | 低 | 数据库索引优化，缓存机制 |
| 前端表单验证复杂性 | 中 | 中 | 详细的测试用例，用户体验测试 |
| 删除操作的数据一致性 | 高 | 低 | 事务处理，级联删除测试 |
| 移动端适配问题 | 中 | 中 | 响应式设计测试，多设备验证 |

---

## 交付清单

### 代码交付物
- [ ] 后端学科管理API服务 (完整可运行)
- [ ] 前端学科管理界面 (完整可运行)
- [ ] 数据验证和业务规则实现
- [ ] 测试用例 (单元测试 + 集成测试 + E2E测试)

### 文档交付物
- [ ] API契约文档 (更新)
- [ ] 后端架构指南 (更新)
- [ ] 前端开发指南 (更新)
- [ ] 用户交互清单 (新建)
- [ ] 变更日志 (更新)

### 功能交付物
- [ ] 学科列表查看功能
- [ ] 学科创建功能 (含表单验证)
- [ ] 学科编辑功能
- [ ] 学科删除功能 (含二次确认)
- [ ] 学科名称唯一性检查
- [ ] 响应式用户界面

---

**文档结束**

*本Sprint-2详细任务计划将确保学科管理功能的高质量实现，为用户提供完整的学科管理体验。*
```