# Sprint-3 详细任务计划 - 访客浏览核心体验

## 文档信息

| 项目 | 内容 |
|------|------|
| **Sprint名称** | Sprint-3: 访客浏览核心体验 |
| **任务ID** | `2270f9a6-66ec-4877-8420-b93331d26975` |
| **优先级** | P0（最高优先级） |
| **预估工期** | 3-4天 |
| **负责人** | Alex (工程师) |
| **依赖任务** | Sprint-2: 学科管理完整功能 |
| **文档版本** | v1.0.0 |
| **创建日期** | 2025-01-08 |
| **规划负责人** | Mike (团队领袖) & Bob (架构师) |
| **状态** | 待执行 |

## Sprint概述

基于《PRD_期末复习平台_v1.0.md》、《Overall_Architecture_期末复习平台.md》和《Task_Planning_期末复习平台_v1.1.md》，将"访客浏览核心体验"切片分解为5个具体的、可执行的子任务。实现访客首页、学科展示、文件结构浏览和基础Markdown内容渲染，支持响应式设计，适配多种设备，实现文件夹优先的排序逻辑。

## 核心业务流程

**访客浏览流程**：
```
访问首页 → 查看学科列表 → 选择学科 → 浏览文件结构 → 选择文件 → 阅读内容
```

## 核心业务规则

1. **内容开放性**：所有内容对访客开放，无需登录
2. **排序规则**：文件夹优先，按字母排序
3. **内容渲染**：Markdown内容正确渲染，支持代码高亮
4. **图片显示**：图片通过相对路径正确显示，支持懒加载
5. **响应式设计**：适配桌面、平板、手机等各种设备
6. **性能要求**：页面加载时间 < 3秒，支持100个并发访客

## 核心设计原则

1. **API契约优先**：所有开发工作以API契约为"法律合同"
2. **用户体验优先**：提供流畅的浏览体验和清晰的导航
3. **性能优化**：懒加载、缓存策略、响应时间优化
4. **错误处理**：友好的错误提示和异常处理

---

## 任务3.0: API契约设计与评审 (API Contract Design & Review)

**优先级**: P-1 (最高)
**预估时间**: 4-6小时
**负责人**: Bob (架构师) + Alex (工程师)

### 任务描述
在所有开发工作开始前，首先要明确定义本次切片所需的所有API接口的详细规格。这份规格就是前后端协作的"法律合同"，是后续所有开发、测试工作的唯一依据。

### 具体实施步骤

#### 3.0.1 设计API端点、方法与数据结构 (DTOs)
明确所有端点、HTTP方法、请求体和响应体的确切字段名与数据类型。

**文件管理API接口设计**：

```javascript
// 1. 获取学科文件结构
GET /api/v1/subjects/:id/files
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "subject": {
      "id": 1,
      "name": "高等数学",
      "description": "高等数学复习资料"
    },
    "file_tree": [
      {
        "id": 1,
        "name": "第一章",
        "type": "folder",
        "parent_id": null,
        "relative_path": "第一章",
        "children": [
          {
            "id": 2,
            "name": "极限与连续.md",
            "type": "file",
            "parent_id": 1,
            "relative_path": "第一章/极限与连续.md",
            "file_size": 2048,
            "mime_type": "text/markdown",
            "created_at": "2025-01-08T10:00:00.000Z"
          }
        ]
      }
    ]
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 2. 获取文件内容
GET /api/v1/files/:id
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "file": {
      "id": 2,
      "name": "极限与连续.md",
      "type": "file",
      "relative_path": "第一章/极限与连续.md",
      "file_size": 2048,
      "mime_type": "text/markdown",
      "created_at": "2025-01-08T10:00:00.000Z"
    },
    "content": "# 极限与连续\n\n## 1.1 极限的定义\n...",
    "subject": {
      "id": 1,
      "name": "高等数学"
    },
    "breadcrumb": [
      { "id": 1, "name": "高等数学", "type": "subject" },
      { "id": 1, "name": "第一章", "type": "folder" },
      { "id": 2, "name": "极限与连续.md", "type": "file" }
    ]
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 3. 获取静态资源文件
GET /api/v1/assets/:fileNodeId
Response: Binary data (image/png, image/jpeg, etc.)
Headers: {
  "Content-Type": "image/png",
  "Cache-Control": "public, max-age=31536000",
  "ETag": "\"abc123\"",
  "Last-Modified": "Wed, 08 Jan 2025 10:00:00 GMT"
}

// 4. 获取学科列表（访客版本，简化信息）
GET /api/v1/subjects/public
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "高等数学",
      "description": "高等数学复习资料",
      "file_count": 15,
      "total_size": 2048576,
      "last_updated": "2025-01-08T10:00:00.000Z",
      "cover_image": "/api/v1/assets/123"
    }
  ],
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 5. 搜索文件（可选功能）
GET /api/v1/search?q=极限&subject_id=1
Response: {
  "success": true,
  "code": 200,
  "message": "搜索完成",
  "data": {
    "query": "极限",
    "total": 3,
    "results": [
      {
        "id": 2,
        "name": "极限与连续.md",
        "type": "file",
        "subject_id": 1,
        "subject_name": "高等数学",
        "relative_path": "第一章/极限与连续.md",
        "highlight": "...关于<mark>极限</mark>的定义...",
        "relevance_score": 0.95
      }
    ]
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 3.0.2 确定状态码与统一错误格式
明确所有成功和失败场景下的HTTP状态码，并统一错误响应的JSON结构。

**错误响应格式**：
```javascript
// 文件不存在错误
{
  "success": false,
  "code": 404,
  "message": "文件不存在",
  "error": "FILE_NOT_FOUND",
  "details": {
    "file_id": 999,
    "requested_path": "/api/v1/files/999"
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 学科不存在错误
{
  "success": false,
  "code": 404,
  "message": "学科不存在",
  "error": "SUBJECT_NOT_FOUND",
  "details": {
    "subject_id": 999
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 文件内容读取错误
{
  "success": false,
  "code": 500,
  "message": "文件内容读取失败",
  "error": "FILE_READ_ERROR",
  "details": {
    "file_id": 2,
    "storage_path": "/uploads/subject1/chapter1/limits.md"
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 静态资源不存在错误
{
  "success": false,
  "code": 404,
  "message": "资源文件不存在",
  "error": "ASSET_NOT_FOUND",
  "details": {
    "file_node_id": 123,
    "asset_path": "/uploads/images/diagram.png"
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 3.0.3 编写初始文档
在 `/docs/architecture/API_Reference.md` 中更新API契约文档。

#### 3.0.4 评审与冻结
由项目负责人最终评审并"冻结"此API契约。

### 验收标准
- [ ] 一份详细的、已评审通过的文件管理API契约文档已创建
- [ ] 所有相关人员都已理解并同意遵守此契约
- [ ] API契约文档已更新到 `/docs/architecture/API_Reference.md`
- [ ] 响应格式和错误处理标准已明确定义
- [ ] 文件树数据结构和面包屑导航数据格式已明确定义

### 相关文件
- `/docs/architecture/API_Reference.md` - API契约文档 (更新)
- `/docs/architecture/Backend_Architecture_and_Guide.md` - 后端架构指南 (参考)

---

## 任务3.1: 环境与数据模型准备 (Setup)

**优先级**: P0 (最高)
**预估时间**: 4-5小时
**负责人**: Alex (工程师)
**依赖任务**: 任务3.0 (API契约设计与评审)

### 任务描述
基于任务3.0中已确定的API契约，确保数据库表结构完全支持文件浏览功能，实现FileNode数据模型和文件服务层，支持文件树查询和内容读取。

### 具体实施步骤

#### 3.1.1 验证和优化数据库表结构
确保file_nodes表结构支持文件树查询和排序需求：

```sql
-- 验证现有file_nodes表结构
SELECT sql FROM sqlite_master WHERE type='table' AND name='file_nodes';

-- 添加必要的索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_parent ON file_nodes(subject_id, parent_id);
CREATE INDEX IF NOT EXISTS idx_file_nodes_type_name ON file_nodes(type, name);

-- 创建视图简化文件树查询
CREATE VIEW IF NOT EXISTS file_tree_view AS
SELECT 
    fn.id,
    fn.subject_id,
    fn.parent_id,
    fn.name,
    fn.type,
    fn.relative_path,
    fn.storage_url,
    fn.file_size,
    fn.mime_type,
    fn.created_at,
    fn.updated_at,
    s.name as subject_name
FROM file_nodes fn
LEFT JOIN subjects s ON fn.subject_id = s.id
ORDER BY fn.type DESC, fn.name ASC;
```

#### 3.1.2 实现FileNode数据模型类
```javascript
// backend/src/models/FileNode.js
class FileNode {
  constructor(db) {
    this.db = db;
  }

  // 获取学科的文件树结构
  getSubjectFileTree(subjectId) {
    const stmt = this.db.prepare(`
      SELECT id, subject_id, parent_id, name, type, relative_path,
             file_size, mime_type, created_at, updated_at
      FROM file_nodes
      WHERE subject_id = ?
      ORDER BY type DESC, name ASC
    `);

    const nodes = stmt.all(subjectId);
    return this.buildTree(nodes);
  }

  // 构建树形结构
  buildTree(nodes, parentId = null) {
    const children = nodes
      .filter(node => node.parent_id === parentId)
      .map(node => ({
        ...node,
        children: node.type === 'folder' ? this.buildTree(nodes, node.id) : []
      }));

    return children;
  }

  // 根据ID获取文件节点
  findById(id) {
    const stmt = this.db.prepare(`
      SELECT fn.*, s.name as subject_name
      FROM file_nodes fn
      LEFT JOIN subjects s ON fn.subject_id = s.id
      WHERE fn.id = ?
    `);
    return stmt.get(id);
  }

  // 获取文件的面包屑路径
  getBreadcrumb(fileId) {
    const file = this.findById(fileId);
    if (!file) return [];

    const breadcrumb = [];
    let currentNode = file;

    // 添加文件本身
    breadcrumb.unshift({
      id: currentNode.id,
      name: currentNode.name,
      type: currentNode.type
    });

    // 向上遍历父节点
    while (currentNode.parent_id) {
      const stmt = this.db.prepare('SELECT * FROM file_nodes WHERE id = ?');
      currentNode = stmt.get(currentNode.parent_id);
      if (currentNode) {
        breadcrumb.unshift({
          id: currentNode.id,
          name: currentNode.name,
          type: currentNode.type
        });
      }
    }

    // 添加学科信息
    breadcrumb.unshift({
      id: file.subject_id,
      name: file.subject_name,
      type: 'subject'
    });

    return breadcrumb;
  }

  // 读取文件内容
  readFileContent(fileId) {
    const file = this.findById(fileId);
    if (!file || file.type !== 'file') {
      return null;
    }

    const fs = require('fs');
    const path = require('path');

    try {
      const filePath = path.join(process.env.UPLOAD_PATH || './uploads', file.storage_url);
      const content = fs.readFileSync(filePath, 'utf8');
      return content;
    } catch (error) {
      console.error('Error reading file:', error);
      return null;
    }
  }

  // 获取静态资源文件路径
  getAssetPath(fileNodeId) {
    const file = this.findById(fileNodeId);
    if (!file) return null;

    const path = require('path');
    return path.join(process.env.UPLOAD_PATH || './uploads', file.storage_url);
  }

  // 搜索文件（可选功能）
  searchFiles(query, subjectId = null) {
    let sql = `
      SELECT fn.*, s.name as subject_name
      FROM file_nodes fn
      LEFT JOIN subjects s ON fn.subject_id = s.id
      WHERE fn.type = 'file' AND fn.name LIKE ?
    `;

    const params = [`%${query}%`];

    if (subjectId) {
      sql += ' AND fn.subject_id = ?';
      params.push(subjectId);
    }

    sql += ' ORDER BY fn.name ASC LIMIT 50';

    const stmt = this.db.prepare(sql);
    return stmt.all(...params);
  }
}

module.exports = FileNode;
```

#### 3.1.3 创建文件服务层
```javascript
// backend/src/services/fileService.js
const FileNode = require('../models/FileNode');
const Subject = require('../models/Subject');

class FileService {
  constructor(fileNodeModel, subjectModel) {
    this.FileNode = fileNodeModel;
    this.Subject = subjectModel;
  }

  async getSubjectFileTree(subjectId) {
    // 验证学科是否存在
    const subject = this.Subject.findById(subjectId);
    if (!subject) {
      const error = new Error('学科不存在');
      error.code = 'SUBJECT_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    // 获取文件树
    const fileTree = this.FileNode.getSubjectFileTree(subjectId);

    return {
      subject: {
        id: subject.id,
        name: subject.name,
        description: subject.description
      },
      file_tree: fileTree
    };
  }

  async getFileContent(fileId) {
    // 获取文件信息
    const file = this.FileNode.findById(fileId);
    if (!file) {
      const error = new Error('文件不存在');
      error.code = 'FILE_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    if (file.type !== 'file') {
      const error = new Error('请求的不是文件');
      error.code = 'NOT_A_FILE';
      error.status = 400;
      throw error;
    }

    // 读取文件内容
    const content = this.FileNode.readFileContent(fileId);
    if (content === null) {
      const error = new Error('文件内容读取失败');
      error.code = 'FILE_READ_ERROR';
      error.status = 500;
      throw error;
    }

    // 获取面包屑导航
    const breadcrumb = this.FileNode.getBreadcrumb(fileId);

    return {
      file: {
        id: file.id,
        name: file.name,
        type: file.type,
        relative_path: file.relative_path,
        file_size: file.file_size,
        mime_type: file.mime_type,
        created_at: file.created_at
      },
      content: content,
      subject: {
        id: file.subject_id,
        name: file.subject_name
      },
      breadcrumb: breadcrumb
    };
  }

  async getAssetFile(fileNodeId) {
    const assetPath = this.FileNode.getAssetPath(fileNodeId);
    if (!assetPath) {
      const error = new Error('资源文件不存在');
      error.code = 'ASSET_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    const fs = require('fs');

    try {
      // 检查文件是否存在
      if (!fs.existsSync(assetPath)) {
        const error = new Error('资源文件不存在');
        error.code = 'ASSET_NOT_FOUND';
        error.status = 404;
        throw error;
      }

      return {
        path: assetPath,
        stats: fs.statSync(assetPath)
      };
    } catch (error) {
      if (error.code === 'ASSET_NOT_FOUND') {
        throw error;
      }

      const newError = new Error('资源文件访问失败');
      newError.code = 'ASSET_ACCESS_ERROR';
      newError.status = 500;
      throw newError;
    }
  }

  async getPublicSubjects() {
    const subjects = this.Subject.findAll();

    // 为访客用户简化学科信息
    return subjects.map(subject => ({
      id: subject.id,
      name: subject.name,
      description: subject.description,
      file_count: subject.file_count || 0,
      total_size: subject.total_size || 0,
      last_updated: subject.updated_at,
      cover_image: null // 可以后续添加封面图片功能
    }));
  }

  async searchFiles(query, subjectId = null) {
    if (!query || query.trim().length < 2) {
      const error = new Error('搜索关键词至少需要2个字符');
      error.code = 'INVALID_SEARCH_QUERY';
      error.status = 400;
      throw error;
    }

    const results = this.FileNode.searchFiles(query.trim(), subjectId);

    return {
      query: query.trim(),
      total: results.length,
      results: results.map(file => ({
        id: file.id,
        name: file.name,
        type: file.type,
        subject_id: file.subject_id,
        subject_name: file.subject_name,
        relative_path: file.relative_path,
        highlight: this.generateHighlight(file.name, query),
        relevance_score: this.calculateRelevance(file.name, query)
      }))
    };
  }

  generateHighlight(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  calculateRelevance(text, query) {
    const lowerText = text.toLowerCase();
    const lowerQuery = query.toLowerCase();

    if (lowerText === lowerQuery) return 1.0;
    if (lowerText.startsWith(lowerQuery)) return 0.9;
    if (lowerText.includes(lowerQuery)) return 0.7;
    return 0.5;
  }
}

module.exports = FileService;
```

### 验收标准
- [ ] 数据库表结构和索引优化完成，支持高效的文件树查询
- [ ] FileNode数据模型类实现完成，支持所有文件操作
- [ ] FileService服务层实现完成，支持业务逻辑处理
- [ ] 文件内容读取和静态资源访问功能正常
- [ ] 面包屑导航数据生成功能正常
- [ ] 更新后端指南文档 (`/docs/architecture/Backend_Architecture_and_Guide.md`)

### 相关文件
- `backend/src/models/FileNode.js` - 文件节点数据模型 (创建)
- `backend/src/services/fileService.js` - 文件业务逻辑服务 (创建)
- `backend/src/config/database.js` - 数据库配置 (更新)
- `/docs/architecture/Backend_Architecture_and_Guide.md` - 后端架构指南 (更新)

---

## 任务3.2: 后端API开发与测试闭环 (Backend Loop)

**优先级**: P0 (最高)
**预估时间**: 10-12小时
**负责人**: Alex (工程师)
**依赖任务**: 任务3.1 (环境与数据模型准备)

### 任务描述
严格按照任务3.0的API契约，实现所有文件管理相关的API接口。采用TDD开发模式，先写测试，再写实现，确保代码质量和业务逻辑正确性。

### 具体实施步骤

#### 3.2.1 创建文件控制器 (TDD方式)

**第一步：编写控制器测试**
```javascript
// tests/integration/fileController.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('File Controller', () => {
  let testSubjectId;
  let testFileId;

  beforeEach(async () => {
    // 创建测试数据
    testSubjectId = await createTestSubject({ name: '测试学科' });
    testFileId = await createTestFile({
      subject_id: testSubjectId,
      name: 'test.md',
      type: 'file',
      content: '# 测试文件\n\n这是测试内容。'
    });
  });

  afterEach(async () => {
    // 清理测试数据
    await clearTestData();
  });

  describe('GET /api/v1/subjects/:id/files', () => {
    test('should return subject file tree', async () => {
      const response = await request(app.callback())
        .get(`/api/v1/subjects/${testSubjectId}/files`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功'
      });

      expect(response.body.data.subject).toMatchObject({
        id: testSubjectId,
        name: '测试学科'
      });

      expect(Array.isArray(response.body.data.file_tree)).toBe(true);
    });

    test('should return 404 for non-existent subject', async () => {
      const response = await request(app.callback())
        .get('/api/v1/subjects/999/files')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        code: 404,
        message: '学科不存在',
        error: 'SUBJECT_NOT_FOUND'
      });
    });
  });

  describe('GET /api/v1/files/:id', () => {
    test('should return file content with breadcrumb', async () => {
      const response = await request(app.callback())
        .get(`/api/v1/files/${testFileId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功'
      });

      expect(response.body.data.file).toMatchObject({
        id: testFileId,
        name: 'test.md',
        type: 'file'
      });

      expect(response.body.data.content).toContain('# 测试文件');
      expect(Array.isArray(response.body.data.breadcrumb)).toBe(true);
    });

    test('should return 404 for non-existent file', async () => {
      const response = await request(app.callback())
        .get('/api/v1/files/999')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        code: 404,
        message: '文件不存在'
      });
    });
  });

  describe('GET /api/v1/subjects/public', () => {
    test('should return public subjects list', async () => {
      const response = await request(app.callback())
        .get('/api/v1/subjects/public')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功'
      });

      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);

      const subject = response.body.data.find(s => s.id === testSubjectId);
      expect(subject).toMatchObject({
        id: testSubjectId,
        name: '测试学科'
      });
    });
  });
});
```

**第二步：实现文件控制器**
```javascript
// backend/src/controllers/fileController.js
const FileService = require('../services/fileService');
const FileNode = require('../models/FileNode');
const Subject = require('../models/Subject');

class FileController {
  constructor() {
    this.fileService = new FileService(
      new FileNode(global.db),
      new Subject(global.db)
    );
  }

  async getSubjectFileTree(ctx) {
    try {
      const { id } = ctx.params;
      const result = await this.fileService.getSubjectFileTree(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getFileContent(ctx) {
    try {
      const { id } = ctx.params;
      const result = await this.fileService.getFileContent(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getAssetFile(ctx) {
    try {
      const { fileNodeId } = ctx.params;
      const result = await this.fileService.getAssetFile(parseInt(fileNodeId));

      const fs = require('fs');
      const path = require('path');
      const mime = require('mime-types');

      // 设置响应头
      const mimeType = mime.lookup(result.path) || 'application/octet-stream';
      ctx.type = mimeType;
      ctx.set('Cache-Control', 'public, max-age=31536000'); // 1年缓存
      ctx.set('ETag', `"${result.stats.mtime.getTime()}"`);
      ctx.set('Last-Modified', result.stats.mtime.toUTCString());

      // 检查If-None-Match头
      if (ctx.headers['if-none-match'] === `"${result.stats.mtime.getTime()}"`) {
        ctx.status = 304;
        return;
      }

      // 返回文件内容
      ctx.body = fs.createReadStream(result.path);
    } catch (error) {
      throw error;
    }
  }

  async getPublicSubjects(ctx) {
    try {
      const subjects = await this.fileService.getPublicSubjects();

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: subjects,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async searchFiles(ctx) {
    try {
      const { q: query, subject_id } = ctx.query;
      const result = await this.fileService.searchFiles(
        query,
        subject_id ? parseInt(subject_id) : null
      );

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '搜索完成',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = FileController;
```
```
